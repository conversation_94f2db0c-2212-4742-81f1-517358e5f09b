#!/usr/bin/env python3
"""
Demo script to showcase the new button styling:
- Blue accent button that turns green on hover/click
- Professional brand icons for Meta and Google
"""

import tkinter as tk
from tkinter import ttk

def create_styling_demo():
    """Create a demo window showing the new button styling"""
    try:
        from ttkthemes import ThemedTk, ThemedStyle
        root = ThemedTk(theme="arc")
        style = ThemedStyle(root)
        
        # Arc theme colors
        bg_color = '#f5f6f7'
        accent_color = '#5294e2'     # Arc blue
        text_color = '#2e3436'
        border_color = '#d3dae3'
        card_color = '#ffffff'
        
    except ImportError:
        print("Using fallback styling (install ttkthemes for Arc theme)")
        root = tk.Tk()
        style = ttk.Style()
        style.theme_use('clam')
        
        # Fallback colors
        bg_color = '#f0f0f0'
        accent_color = '#0078d4'
        text_color = '#323130'
        border_color = '#d1d1d1'
        card_color = '#ffffff'
    
    root.title("Button Styling Demo - Meta Ads Transformer")
    root.geometry("500x400")
    root.configure(bg=bg_color)
    
    # FIXED Process button styling - reliable and visible
    style.configure('Accent.TButton',
                   font=('Segoe UI', 10, 'bold'),
                   background=accent_color,          # Blue background
                   foreground='white',               # White text
                   borderwidth=2,
                   relief='raised',
                   focuscolor='none',
                   padding=(15, 10))

    # Simple, reliable hover states
    style.map('Accent.TButton',
             background=[('active', '#27ae60'),     # Green on hover
                        ('pressed', '#1e7e34'),     # Dark green when pressed
                        ('disabled', '#6c757d')],   # Gray when disabled
             foreground=[('active', 'white'),       # White text on hover
                        ('pressed', 'white'),       # White text when pressed
                        ('disabled', 'white')],     # White text when disabled
             relief=[('pressed', 'sunken'),         # Sunken when pressed
                    ('!pressed', 'raised')])        # Raised when not pressed
    
    # Main container
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # Title
    title_label = ttk.Label(main_frame, text="Button Styling Demo", 
                           font=('Segoe UI', 16, 'bold'))
    title_label.pack(pady=(0, 20))
    
    # Data Source Section (showing brand icons)
    source_frame = ttk.LabelFrame(main_frame, text="📊 Data Source with Brand Icons", padding="15")
    source_frame.pack(fill=tk.X, pady=(0, 20))
    
    data_source_var = tk.StringVar(value="meta")
    
    # Meta Ads option with brand icon
    meta_frame = ttk.Frame(source_frame)
    meta_frame.pack(fill=tk.X, pady=(0, 8))
    
    meta_icon = ttk.Label(meta_frame, text="ⓕ", foreground="#1877f2", font=("Arial", 16, "bold"))
    meta_icon.pack(side=tk.LEFT, padx=(0, 8))
    
    ttk.Radiobutton(meta_frame, text="Meta Ads (Facebook)", 
                   variable=data_source_var, value="meta").pack(side=tk.LEFT)
    
    # Google Ads option with brand icon
    google_frame = ttk.Frame(source_frame)
    google_frame.pack(fill=tk.X)
    
    google_icon = ttk.Label(google_frame, text="Ⓖ", foreground="#ea4335", font=("Arial", 16, "bold"))
    google_icon.pack(side=tk.LEFT, padx=(0, 8))
    
    ttk.Radiobutton(google_frame, text="Google Ads", 
                   variable=data_source_var, value="google").pack(side=tk.LEFT)
    
    # Button Demo Section
    button_frame = ttk.LabelFrame(main_frame, text="🎨 Enhanced Button Styling", padding="15")
    button_frame.pack(fill=tk.X, pady=(0, 20))
    
    # Instructions
    instructions = ttk.Label(button_frame, 
                           text="Hover over the Process button to see it change from blue to green!",
                           font=('Segoe UI', 10))
    instructions.pack(pady=(0, 15))
    
    # Demo buttons
    button_container = ttk.Frame(button_frame)
    button_container.pack()
    
    # Normal button
    normal_btn = ttk.Button(button_container, text="Normal Button", width=20)
    normal_btn.pack(pady=5)
    
    # Enhanced Process button (blue to green)
    process_btn = ttk.Button(button_container, text="🔍 Process & Validate Data", 
                           style='Accent.TButton', width=25)
    process_btn.pack(pady=5)
    
    # Status info
    status_frame = ttk.Frame(main_frame)
    status_frame.pack(fill=tk.X, pady=(10, 0))
    
    status_text = """
✅ Button Styling Features:
• Blue accent color (#5294e2) for normal state
• Green hover color (#27ae60) for interactive feedback  
• Darker green (#229954) when pressed
• Professional brand icons (ⓕ for Meta, Ⓖ for Google)
• Enhanced padding and typography
• Arc theme integration
    """
    
    status_label = ttk.Label(status_frame, text=status_text, 
                           font=('Segoe UI', 9), justify=tk.LEFT)
    status_label.pack(anchor=tk.W)
    
    # Demo interaction
    def on_process_click():
        """Demo function for process button"""
        process_btn.config(text="✅ Processing Complete!")
        root.after(2000, lambda: process_btn.config(text="🔍 Process & Validate Data"))
    
    process_btn.config(command=on_process_click)
    
    root.mainloop()

def main():
    """Main demo function"""
    print("🎨 Button Styling Demo - Meta Ads Transformer")
    print("=" * 50)
    print("This demo shows:")
    print("✅ Blue Process button that turns green on hover")
    print("✅ Professional brand icons for Meta and Google")
    print("✅ Arc theme integration")
    print("\nClose the demo window when finished.")
    print()
    
    create_styling_demo()

if __name__ == "__main__":
    main()
