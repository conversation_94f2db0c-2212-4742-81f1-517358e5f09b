"""
Enhanced Ads Data Transformer
Supports both Meta Ads and Google Ads data processing
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import numpy as np
from datetime import datetime
import os
import traceback

# Import processors
from google_ads_processor import GoogleAdsProcessor


class DataSourceManager:
    """Factory for data processors"""
    
    @staticmethod
    def get_processor(source_type):
        if source_type == "Google Ads":
            return GoogleAdsProcessor()
        elif source_type == "Meta Ads":
            return MetaAdsProcessor()
        else:
            raise ValueError(f"Unsupported data source: {source_type}")


class MetaAdsProcessor:
    """Meta Ads processor (extracted from original implementation)"""
    
    def __init__(self):
        self.required_columns = ['Reporting ends', 'Amount spent (USD)', 'Results', 'Reach', 'Impressions']
    
    def validate_csv(self, file_path):
        """Load and validate Meta Ads CSV file"""
        try:
            # Load CSV
            df = pd.read_csv(file_path)
            
            # Skip summary row (first row)
            if len(df) > 1:
                df = df.iloc[1:].copy()
            
            # Clean column names
            df.columns = [c.strip() for c in df.columns]
            
            # Validate required columns
            missing_columns = [col for col in self.required_columns if col not in df.columns]
            
            if missing_columns:
                raise ValueError(f"Required columns missing: {', '.join(missing_columns)}")
            
            # Convert and validate data types
            df['Reporting ends'] = pd.to_datetime(df['Reporting ends'], errors='coerce')
            
            for col in ['Amount spent (USD)', 'Results', 'Reach', 'Impressions']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # Remove rows with invalid dates
            df = df[df['Reporting ends'].notnull()].copy()
            
            if len(df) == 0:
                raise ValueError("No valid data found after cleaning")
            
            return df
            
        except Exception as e:
            raise Exception(f"Error validating Meta Ads CSV: {str(e)}")
    
    def aggregate_by_period(self, df, period='D'):
        """Aggregate Meta Ads data by time period"""
        try:
            # Group by period
            grouped = df.groupby(df['Reporting ends'].dt.to_period(period)).agg({
                'Amount spent (USD)': 'sum',
                'Results': 'sum',
                'Reach': 'sum',
                'Impressions': 'sum',
                'Ad name': 'count'  # Count entries
            }).reset_index()
            
            # Rename columns
            grouped = grouped.rename(columns={
                'Reporting ends': 'Period',
                'Amount spent (USD)': 'Total_Spend',
                'Results': 'Total_Results',
                'Reach': 'Total_Reach',
                'Impressions': 'Total_Impressions',
                'Ad name': 'Entry_Count'
            })
            
            # Calculate frequency (handle division by zero)
            grouped['Frequency'] = np.where(
                grouped['Total_Reach'] > 0,
                grouped['Total_Impressions'] / grouped['Total_Reach'],
                0
            )
            
            # Format period
            grouped['Period'] = grouped['Period'].astype(str)
            
            return grouped
            
        except Exception as e:
            raise Exception(f"Error in aggregation: {str(e)}")
    
    def generate_quality_report(self, df):
        """Generate quality report for Meta Ads data"""
        try:
            report = {
                'total_records': len(df),
                'date_range': (df['Reporting ends'].min(), df['Reporting ends'].max()),
                'total_spend': df['Amount spent (USD)'].sum(),
                'total_results': df['Results'].sum(),
                'total_reach': df['Reach'].sum(),
                'total_impressions': df['Impressions'].sum()
            }
            
            # Data completeness
            report['data_completeness'] = {}
            for col in ['Amount spent (USD)', 'Results', 'Reach', 'Impressions']:
                null_count = df[col].isnull().sum()
                null_pct = (null_count / len(df)) * 100
                report['data_completeness'][col] = {
                    'missing_count': null_count,
                    'missing_percentage': null_pct
                }
            
            return report
            
        except Exception as e:
            raise Exception(f"Error generating quality report: {str(e)}")
    
    def format_quality_report_text(self, quality_report):
        """Format quality report as readable text"""
        try:
            report_lines = []
            report_lines.append("=== META ADS DATA QUALITY REPORT ===\n")
            
            # Basic stats
            report_lines.append(f"Total Records: {quality_report['total_records']:,}")
            report_lines.append(f"Date Range: {quality_report['date_range'][0].strftime('%Y-%m-%d')} to {quality_report['date_range'][1].strftime('%Y-%m-%d')}")
            report_lines.append(f"Total Spend: ${quality_report['total_spend']:,.2f}")
            report_lines.append(f"Total Results: {quality_report['total_results']:,.0f}")
            report_lines.append(f"Total Reach: {quality_report['total_reach']:,.0f}")
            report_lines.append(f"Total Impressions: {quality_report['total_impressions']:,.0f}")
            report_lines.append("")
            
            # Data completeness
            report_lines.append("=== DATA COMPLETENESS ===")
            for col, stats in quality_report['data_completeness'].items():
                report_lines.append(f"{col}: {stats['missing_percentage']:.1f}% missing ({stats['missing_count']:,} records)")
            
            return "\n".join(report_lines)
            
        except Exception as e:
            return f"Error formatting quality report: {str(e)}"


class EnhancedAdsTransformer:
    """Enhanced Ads Transformer supporting multiple data sources"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("Enhanced Ads Data Transformer - Meta & Google Ads")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f0f0f0')
        
        # Data storage
        self.raw_data = None
        self.processed_data = None
        self.daily_aggregated = None
        self.monthly_summary = None
        self.quality_report = {}
        
        # Current data source and processor
        self.current_data_source = tk.StringVar(value="Meta Ads")
        self.current_processor = None
        
        self.setup_ui()
        self.switch_data_source()  # Initialize with default processor
        
    def setup_ui(self):
        """Setup the main UI"""
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Enhanced Ads Data Transformer", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Control Panel
        self.setup_control_panel(main_frame)
        
        # Data Quality Panel
        self.setup_quality_panel(main_frame)
        
        # Data Preview Panel
        self.setup_preview_panel(main_frame)
        
        # Status Bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready - Select data source and load a CSV file to begin")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
    
    def setup_control_panel(self, parent):
        """Setup control panel with data source selection"""
        # Control Panel Frame
        control_frame = ttk.LabelFrame(parent, text="Controls", padding="10")
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N), padx=(0, 10))
        
        # Data Source Selection
        ttk.Label(control_frame, text="Data Source:").grid(row=0, column=0, sticky=tk.W, pady=5)
        data_source_combo = ttk.Combobox(control_frame, textvariable=self.current_data_source,
                                        values=["Meta Ads", "Google Ads"], state="readonly", width=15)
        data_source_combo.grid(row=0, column=1, sticky=tk.W, padx=(5, 0), pady=5)
        data_source_combo.bind('<<ComboboxSelected>>', lambda e: self.switch_data_source())
        
        # Load CSV Button
        load_btn = ttk.Button(control_frame, text="📁 Load CSV", 
                             command=self.load_csv, width=15)
        load_btn.grid(row=1, column=0, pady=5, sticky=tk.W)
        
        # Transform Options
        ttk.Label(control_frame, text="Transform Options:").grid(row=2, column=0, sticky=tk.W, pady=(10, 5))
        
        self.daily_var = tk.BooleanVar(value=True)
        self.monthly_var = tk.BooleanVar(value=True)
        
        ttk.Checkbutton(control_frame, text="Daily Aggregation", 
                       variable=self.daily_var).grid(row=3, column=0, sticky=tk.W)
        ttk.Checkbutton(control_frame, text="Monthly Summary", 
                       variable=self.monthly_var).grid(row=4, column=0, sticky=tk.W)
        
        # Transform Button
        transform_btn = ttk.Button(control_frame, text="🔄 Transform Data", 
                                  command=self.transform_data, width=15)
        transform_btn.grid(row=5, column=0, pady=(10, 5), sticky=tk.W)
        
        # Export Buttons
        ttk.Label(control_frame, text="Export:").grid(row=6, column=0, sticky=tk.W, pady=(10, 5))
        
        export_daily_btn = ttk.Button(control_frame, text="📊 Export Daily", 
                                     command=self.export_daily, width=15)
        export_daily_btn.grid(row=7, column=0, pady=2, sticky=tk.W)
        
        export_monthly_btn = ttk.Button(control_frame, text="📈 Export Monthly",
                                       command=self.export_monthly, width=15)
        export_monthly_btn.grid(row=8, column=0, pady=2, sticky=tk.W)

    def setup_quality_panel(self, parent):
        """Setup data quality panel"""
        # Data Quality Panel
        quality_frame = ttk.LabelFrame(parent, text="Data Quality Report", padding="10")
        quality_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        quality_frame.columnconfigure(0, weight=1)
        quality_frame.rowconfigure(0, weight=1)

        # Quality report text area
        self.quality_text = tk.Text(quality_frame, height=15, width=50, wrap=tk.WORD)
        quality_scroll = ttk.Scrollbar(quality_frame, orient=tk.VERTICAL, command=self.quality_text.yview)
        self.quality_text.configure(yscrollcommand=quality_scroll.set)

        self.quality_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        quality_scroll.grid(row=0, column=1, sticky=(tk.N, tk.S))

        self.quality_text.config(state=tk.DISABLED)

    def setup_preview_panel(self, parent):
        """Setup data preview panel"""
        # Data Preview Panel
        preview_frame = ttk.LabelFrame(parent, text="Data Preview", padding="10")
        preview_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S))
        preview_frame.columnconfigure(0, weight=1)
        preview_frame.rowconfigure(1, weight=1)

        # Notebook for different views
        self.notebook = ttk.Notebook(preview_frame)
        self.notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # Initialize tabs (will be populated based on data source)
        self.setup_data_tabs()

    def setup_data_tabs(self):
        """Setup data tabs based on current data source"""
        # Clear existing tabs
        for tab in self.notebook.tabs():
            self.notebook.forget(tab)

        if self.current_data_source.get() == "Meta Ads":
            self.setup_meta_ads_tabs()
        else:  # Google Ads
            self.setup_google_ads_tabs()

    def setup_meta_ads_tabs(self):
        """Setup tabs for Meta Ads data"""
        # Monthly Summary Tab
        monthly_frame = ttk.Frame(self.notebook)
        self.notebook.add(monthly_frame, text="Monthly Summary")

        columns = ('Month', 'Amount Spent (USD)', 'Results', 'Entry Count', 'Avg Frequency')
        self.monthly_tree = ttk.Treeview(monthly_frame, columns=columns, show='headings', height=10)

        for col in columns:
            self.monthly_tree.heading(col, text=col)
            self.monthly_tree.column(col, width=120, anchor=tk.CENTER)

        monthly_scroll_y = ttk.Scrollbar(monthly_frame, orient=tk.VERTICAL, command=self.monthly_tree.yview)
        monthly_scroll_x = ttk.Scrollbar(monthly_frame, orient=tk.HORIZONTAL, command=self.monthly_tree.xview)
        self.monthly_tree.configure(yscrollcommand=monthly_scroll_y.set, xscrollcommand=monthly_scroll_x.set)

        self.monthly_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        monthly_scroll_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        monthly_scroll_x.grid(row=1, column=0, sticky=(tk.W, tk.E))

        monthly_frame.columnconfigure(0, weight=1)
        monthly_frame.rowconfigure(0, weight=1)

        # Daily Data Tab
        daily_frame = ttk.Frame(self.notebook)
        self.notebook.add(daily_frame, text="Daily Aggregated")

        columns = ('Date', 'Total Spend', 'Total Results', 'Total Reach', 'Total Impressions', 'Frequency')
        self.daily_tree = ttk.Treeview(daily_frame, columns=columns, show='headings', height=10)

        for col in columns:
            self.daily_tree.heading(col, text=col)
            self.daily_tree.column(col, width=100, anchor=tk.CENTER)

        daily_scroll_y = ttk.Scrollbar(daily_frame, orient=tk.VERTICAL, command=self.daily_tree.yview)
        daily_scroll_x = ttk.Scrollbar(daily_frame, orient=tk.HORIZONTAL, command=self.daily_tree.xview)
        self.daily_tree.configure(yscrollcommand=daily_scroll_y.set, xscrollcommand=daily_scroll_x.set)

        self.daily_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        daily_scroll_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        daily_scroll_x.grid(row=1, column=0, sticky=(tk.W, tk.E))

        daily_frame.columnconfigure(0, weight=1)
        daily_frame.rowconfigure(0, weight=1)

        # Raw Data Tab
        raw_frame = ttk.Frame(self.notebook)
        self.notebook.add(raw_frame, text="Raw Data Sample")

        columns = ('Date', 'Ad Name', 'Amount Spent', 'Results', 'Reach', 'Impressions')
        self.raw_tree = ttk.Treeview(raw_frame, columns=columns, show='headings', height=10)

        for col in columns:
            self.raw_tree.heading(col, text=col)
            self.raw_tree.column(col, width=120, anchor=tk.CENTER)

        raw_scroll_y = ttk.Scrollbar(raw_frame, orient=tk.VERTICAL, command=self.raw_tree.yview)
        raw_scroll_x = ttk.Scrollbar(raw_frame, orient=tk.HORIZONTAL, command=self.raw_tree.xview)
        self.raw_tree.configure(yscrollcommand=raw_scroll_y.set, xscrollcommand=raw_scroll_x.set)

        self.raw_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        raw_scroll_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        raw_scroll_x.grid(row=1, column=0, sticky=(tk.W, tk.E))

        raw_frame.columnconfigure(0, weight=1)
        raw_frame.rowconfigure(0, weight=1)

    def setup_google_ads_tabs(self):
        """Setup tabs for Google Ads data"""
        # Monthly Summary Tab
        monthly_frame = ttk.Frame(self.notebook)
        self.notebook.add(monthly_frame, text="Monthly Summary")

        columns = ('Month', 'Total Cost', 'Total Clicks', 'Total Conversions', 'Avg CTR', 'Avg CPC')
        self.monthly_tree = ttk.Treeview(monthly_frame, columns=columns, show='headings', height=10)

        for col in columns:
            self.monthly_tree.heading(col, text=col)
            self.monthly_tree.column(col, width=120, anchor=tk.CENTER)

        monthly_scroll_y = ttk.Scrollbar(monthly_frame, orient=tk.VERTICAL, command=self.monthly_tree.yview)
        monthly_scroll_x = ttk.Scrollbar(monthly_frame, orient=tk.HORIZONTAL, command=self.monthly_tree.xview)
        self.monthly_tree.configure(yscrollcommand=monthly_scroll_y.set, xscrollcommand=monthly_scroll_x.set)

        self.monthly_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        monthly_scroll_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        monthly_scroll_x.grid(row=1, column=0, sticky=(tk.W, tk.E))

        monthly_frame.columnconfigure(0, weight=1)
        monthly_frame.rowconfigure(0, weight=1)

        # Daily Data Tab
        daily_frame = ttk.Frame(self.notebook)
        self.notebook.add(daily_frame, text="Daily Aggregated")

        columns = ('Date', 'Total Cost', 'Total Clicks', 'Total Impressions', 'Total Conversions', 'Avg CTR', 'Avg CPC')
        self.daily_tree = ttk.Treeview(daily_frame, columns=columns, show='headings', height=10)

        for col in columns:
            self.daily_tree.heading(col, text=col)
            self.daily_tree.column(col, width=100, anchor=tk.CENTER)

        daily_scroll_y = ttk.Scrollbar(daily_frame, orient=tk.VERTICAL, command=self.daily_tree.yview)
        daily_scroll_x = ttk.Scrollbar(daily_frame, orient=tk.HORIZONTAL, command=self.daily_tree.xview)
        self.daily_tree.configure(yscrollcommand=daily_scroll_y.set, xscrollcommand=daily_scroll_x.set)

        self.daily_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        daily_scroll_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        daily_scroll_x.grid(row=1, column=0, sticky=(tk.W, tk.E))

        daily_frame.columnconfigure(0, weight=1)
        daily_frame.rowconfigure(0, weight=1)

        # Campaign Performance Tab
        campaign_frame = ttk.Frame(self.notebook)
        self.notebook.add(campaign_frame, text="Campaign Performance")

        columns = ('Campaign', 'Total Cost', 'Clicks', 'Conversions', 'CTR', 'CPC', 'Conv Rate', 'ROI Score')
        self.campaign_tree = ttk.Treeview(campaign_frame, columns=columns, show='headings', height=10)

        for col in columns:
            self.campaign_tree.heading(col, text=col)
            self.campaign_tree.column(col, width=120, anchor=tk.CENTER)

        campaign_scroll_y = ttk.Scrollbar(campaign_frame, orient=tk.VERTICAL, command=self.campaign_tree.yview)
        campaign_scroll_x = ttk.Scrollbar(campaign_frame, orient=tk.HORIZONTAL, command=self.campaign_tree.xview)
        self.campaign_tree.configure(yscrollcommand=campaign_scroll_y.set, xscrollcommand=campaign_scroll_x.set)

        self.campaign_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        campaign_scroll_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        campaign_scroll_x.grid(row=1, column=0, sticky=(tk.W, tk.E))

        campaign_frame.columnconfigure(0, weight=1)
        campaign_frame.rowconfigure(0, weight=1)

        # Raw Data Tab
        raw_frame = ttk.Frame(self.notebook)
        self.notebook.add(raw_frame, text="Raw Data Sample")

        columns = ('Date', 'Campaign', 'Clicks', 'Impressions', 'Cost', 'Conversions', 'CTR', 'CPC')
        self.raw_tree = ttk.Treeview(raw_frame, columns=columns, show='headings', height=10)

        for col in columns:
            self.raw_tree.heading(col, text=col)
            self.raw_tree.column(col, width=120, anchor=tk.CENTER)

        raw_scroll_y = ttk.Scrollbar(raw_frame, orient=tk.VERTICAL, command=self.raw_tree.yview)
        raw_scroll_x = ttk.Scrollbar(raw_frame, orient=tk.HORIZONTAL, command=self.raw_tree.xview)
        self.raw_tree.configure(yscrollcommand=raw_scroll_y.set, xscrollcommand=raw_scroll_x.set)

        self.raw_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        raw_scroll_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        raw_scroll_x.grid(row=1, column=0, sticky=(tk.W, tk.E))

        raw_frame.columnconfigure(0, weight=1)
        raw_frame.rowconfigure(0, weight=1)

    def switch_data_source(self):
        """Switch between data sources and update UI"""
        try:
            # Get the appropriate processor
            self.current_processor = DataSourceManager.get_processor(self.current_data_source.get())

            # Update UI tabs
            self.setup_data_tabs()

            # Clear existing data
            self.raw_data = None
            self.processed_data = None
            self.daily_aggregated = None
            self.monthly_summary = None
            self.quality_report = {}

            # Update status
            self.status_var.set(f"Switched to {self.current_data_source.get()} - Load a CSV file to begin")

            # Clear quality report
            self.quality_text.config(state=tk.NORMAL)
            self.quality_text.delete(1.0, tk.END)
            self.quality_text.config(state=tk.DISABLED)

        except Exception as e:
            self.handle_error("Error switching data source", e)

    def load_csv(self):
        """Load CSV file based on current data source"""
        try:
            data_source = self.current_data_source.get()
            file_path = filedialog.askopenfilename(
                title=f"Select {data_source} CSV File",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
            )

            if not file_path:
                return

            self.status_var.set("Loading CSV file...")
            self.root.update()

            # Load and validate data using current processor
            self.raw_data = self.current_processor.validate_csv(file_path)

            if self.raw_data is not None:
                self.update_raw_data_display()
                self.generate_quality_report()
                self.status_var.set(f"Loaded {len(self.raw_data)} records successfully")
            else:
                self.status_var.set("Failed to load CSV file")

        except Exception as e:
            self.handle_error("Error loading CSV", e)

    def transform_data(self):
        """Transform data using current processor"""
        if self.raw_data is None:
            messagebox.showwarning("No Data", "Please load a CSV file first")
            return

        try:
            self.status_var.set("Transforming data...")
            self.root.update()

            if self.monthly_var.get():
                self.monthly_summary = self.current_processor.aggregate_by_period(self.raw_data, 'M')
                self.update_monthly_display()

            if self.daily_var.get():
                self.daily_aggregated = self.current_processor.aggregate_by_period(self.raw_data, 'D')
                self.update_daily_display()

            # For Google Ads, also calculate campaign performance
            if self.current_data_source.get() == "Google Ads":
                self.campaign_performance = self.current_processor.calculate_campaign_performance(self.raw_data)
                self.update_campaign_display()

            self.status_var.set("Data transformation completed successfully")

        except Exception as e:
            self.handle_error("Error transforming data", e)

    def update_raw_data_display(self):
        """Update raw data display based on data source"""
        # Clear existing data
        for item in self.raw_tree.get_children():
            self.raw_tree.delete(item)

        if self.raw_data is not None:
            # Display first 100 rows
            display_data = self.raw_data.head(100)

            if self.current_data_source.get() == "Meta Ads":
                for _, row in display_data.iterrows():
                    values = (
                        row['Reporting ends'].strftime('%Y-%m-%d') if pd.notnull(row['Reporting ends']) else '',
                        row.get('Ad name', ''),
                        f"${row['Amount spent (USD)']:,.2f}" if pd.notnull(row['Amount spent (USD)']) else '',
                        f"{row['Results']:,.0f}" if pd.notnull(row['Results']) else '',
                        f"{row['Reach']:,.0f}" if pd.notnull(row['Reach']) else '',
                        f"{row['Impressions']:,.0f}" if pd.notnull(row['Impressions']) else ''
                    )
                    self.raw_tree.insert('', 'end', values=values)
            else:  # Google Ads
                for _, row in display_data.iterrows():
                    values = (
                        row['Date'].strftime('%Y-%m-%d') if pd.notnull(row['Date']) else '',
                        row.get('Campaign', ''),
                        f"{row['Clicks']:,.0f}" if pd.notnull(row['Clicks']) else '',
                        f"{row['Impressions']:,.0f}" if pd.notnull(row['Impressions']) else '',
                        f"${row['Cost']:,.2f}" if pd.notnull(row['Cost']) else '',
                        f"{row['Conversions']:,.0f}" if pd.notnull(row['Conversions']) else '',
                        f"{row['CTR']:.2f}%" if pd.notnull(row['CTR']) else '',
                        f"${row['Avg. CPC']:,.2f}" if pd.notnull(row['Avg. CPC']) else ''
                    )
                    self.raw_tree.insert('', 'end', values=values)

    def update_monthly_display(self):
        """Update monthly summary display"""
        # Clear existing data
        for item in self.monthly_tree.get_children():
            self.monthly_tree.delete(item)

        if self.monthly_summary is not None:
            for _, row in self.monthly_summary.iterrows():
                if self.current_data_source.get() == "Meta Ads":
                    values = (
                        row['Period'],
                        f"${row['Total_Spend']:,.2f}",
                        f"{row['Total_Results']:,.0f}",
                        f"{row['Entry_Count']:,}",
                        f"{row['Frequency']:.2f}"
                    )
                else:  # Google Ads
                    values = (
                        row['Period'],
                        f"${row['Total_Cost']:,.2f}",
                        f"{row['Total_Clicks']:,.0f}",
                        f"{row['Total_Conversions']:,.0f}",
                        f"{row['Average_CTR']:.2f}%",
                        f"${row['Average_CPC']:.2f}"
                    )
                self.monthly_tree.insert('', 'end', values=values)

    def update_daily_display(self):
        """Update daily aggregated display"""
        # Clear existing data
        for item in self.daily_tree.get_children():
            self.daily_tree.delete(item)

        if self.daily_aggregated is not None:
            for _, row in self.daily_aggregated.iterrows():
                if self.current_data_source.get() == "Meta Ads":
                    values = (
                        row['Period'],
                        f"${row['Total_Spend']:,.2f}",
                        f"{row['Total_Results']:,.0f}",
                        f"{row['Total_Reach']:,.0f}",
                        f"{row['Total_Impressions']:,.0f}",
                        f"{row['Frequency']:.2f}"
                    )
                else:  # Google Ads
                    values = (
                        row['Period'],
                        f"${row['Total_Cost']:,.2f}",
                        f"{row['Total_Clicks']:,.0f}",
                        f"{row['Total_Impressions']:,.0f}",
                        f"{row['Total_Conversions']:,.0f}",
                        f"{row['Average_CTR']:.2f}%",
                        f"${row['Average_CPC']:.2f}"
                    )
                self.daily_tree.insert('', 'end', values=values)

    def update_campaign_display(self):
        """Update campaign performance display (Google Ads only)"""
        if not hasattr(self, 'campaign_tree'):
            return

        # Clear existing data
        for item in self.campaign_tree.get_children():
            self.campaign_tree.delete(item)

        if hasattr(self, 'campaign_performance') and self.campaign_performance is not None:
            for _, row in self.campaign_performance.iterrows():
                values = (
                    row['Campaign'],
                    f"${row['Cost']:,.2f}",
                    f"{row['Clicks']:,.0f}",
                    f"{row['Conversions']:,.0f}",
                    f"{row['CTR']:.2f}%",
                    f"${row['CPC']:,.2f}",
                    f"{row['Conversion_Rate']:.2f}%",
                    f"{row['ROI_Score']:.4f}"
                )
                self.campaign_tree.insert('', 'end', values=values)

    def generate_quality_report(self):
        """Generate quality report using current processor"""
        if self.raw_data is None:
            return

        try:
            self.quality_report = self.current_processor.generate_quality_report(self.raw_data)
            report_text = self.current_processor.format_quality_report_text(self.quality_report)

            # Display report
            self.quality_text.config(state=tk.NORMAL)
            self.quality_text.delete(1.0, tk.END)
            self.quality_text.insert(1.0, report_text)
            self.quality_text.config(state=tk.DISABLED)

        except Exception as e:
            self.handle_error("Error generating quality report", e)

    def export_daily(self):
        """Export daily aggregated data"""
        if self.daily_aggregated is None:
            messagebox.showwarning("No Data", "Please transform data first")
            return

        data_source = self.current_data_source.get().lower().replace(" ", "_")
        self.export_data(self.daily_aggregated, f"{data_source}_daily_aggregated")

    def export_monthly(self):
        """Export monthly summary data"""
        if self.monthly_summary is None:
            messagebox.showwarning("No Data", "Please transform data first")
            return

        data_source = self.current_data_source.get().lower().replace(" ", "_")
        self.export_data(self.monthly_summary, f"{data_source}_monthly_summary")

    def export_data(self, data, default_name):
        """Export data to CSV file"""
        try:
            file_path = filedialog.asksaveasfilename(
                title=f"Save {default_name.replace('_', ' ').title()}",
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                initialfile=f"{default_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            )

            if file_path:
                data.to_csv(file_path, index=False)
                messagebox.showinfo("Export Successful", f"Data exported to {file_path}")
                self.status_var.set(f"Exported {len(data)} records to {os.path.basename(file_path)}")

        except Exception as e:
            self.handle_error("Error exporting data", e)

    def handle_error(self, title, error):
        """Handle errors with consistent messaging"""
        error_msg = f"{title}: {str(error)}"
        print(f"ERROR: {error_msg}")
        print(traceback.format_exc())
        messagebox.showerror(title, error_msg)
        self.status_var.set(f"Error: {str(error)}")


def main():
    """Main application entry point"""
    root = tk.Tk()
    app = EnhancedAdsTransformer(root)
    root.mainloop()


if __name__ == "__main__":
    main()
