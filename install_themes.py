#!/usr/bin/env python3
"""
Installation script for ttkthemes and theme testing
"""

import subprocess
import sys
import os

def install_package(package_name):
    """Install a package using pip"""
    try:
        print(f"Installing {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✓ {package_name} installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install {package_name}: {e}")
        return False

def test_import():
    """Test if ttkthemes can be imported"""
    try:
        from ttkthemes import ThemedTk, ThemedStyle
        print("✓ ttkthemes imported successfully!")
        return True
    except ImportError as e:
        print(f"✗ Failed to import ttkthemes: {e}")
        return False

def list_available_themes():
    """List all available themes"""
    try:
        from ttkthemes import ThemedStyle
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        style = ThemedStyle(root)
        themes = style.theme_names()
        
        print("\n📋 Available themes:")
        for i, theme in enumerate(sorted(themes), 1):
            print(f"  {i:2d}. {theme}")
        
        root.destroy()
        return themes
    except Exception as e:
        print(f"✗ Failed to list themes: {e}")
        return []

def test_arc_theme():
    """Test the Arc theme specifically"""
    try:
        from ttkthemes import ThemedTk
        import tkinter as tk
        from tkinter import ttk
        
        print("\n🎨 Testing Arc theme...")
        
        # Create a test window
        root = ThemedTk(theme="arc")
        root.title("Arc Theme Test")
        root.geometry("400x300")
        
        # Add some test widgets
        frame = ttk.Frame(root, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(frame, text="Arc Theme Test", font=('Segoe UI', 14, 'bold')).pack(pady=10)
        
        ttk.Button(frame, text="Normal Button").pack(pady=5)
        ttk.Button(frame, text="Accent Button", style='Accent.TButton').pack(pady=5)
        
        # Test LabelFrame
        lf = ttk.LabelFrame(frame, text="Test Section", padding="10")
        lf.pack(fill=tk.X, pady=10)
        
        ttk.Label(lf, text="This is how the Arc theme looks!").pack()
        ttk.Entry(lf, width=30).pack(pady=5)
        
        print("✓ Arc theme test window created!")
        print("  Close the test window to continue...")
        
        root.mainloop()
        return True
        
    except Exception as e:
        print(f"✗ Failed to test Arc theme: {e}")
        return False

def main():
    """Main installation and testing function"""
    print("🎨 Meta Ads Transformer - Theme Installation & Testing")
    print("=" * 55)
    
    # Step 1: Install ttkthemes
    if not install_package("ttkthemes"):
        print("\n❌ Installation failed. Please install manually:")
        print("   pip install ttkthemes")
        return False
    
    # Step 2: Test import
    if not test_import():
        print("\n❌ Import test failed. Please check your installation.")
        return False
    
    # Step 3: List available themes
    themes = list_available_themes()
    
    # Step 4: Test Arc theme
    if "arc" in themes:
        print(f"\n✓ Arc theme is available!")
        
        response = input("\n🎨 Would you like to test the Arc theme? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            test_arc_theme()
    else:
        print(f"\n⚠ Arc theme not found in available themes.")
        print("Available themes include:", ", ".join(themes[:5]), "...")
    
    print("\n🎉 Installation and testing complete!")
    print("\nYou can now run your Meta Ads Transformer with the new Arc theme:")
    print("   python meta_ads_transformer_complete.py")
    
    return True

if __name__ == "__main__":
    main()
