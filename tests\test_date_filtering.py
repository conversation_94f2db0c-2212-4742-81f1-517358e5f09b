#!/usr/bin/env python3
"""
Test the advanced date filtering system in Meta Ads Transformer Complete
"""

import sys
import os
sys.path.append('..')

def test_date_filtering_ui_components():
    """Test that all date filtering UI components are present"""
    print("🔍 TESTING DATE FILTERING UI COMPONENTS")
    print("=" * 60)
    
    try:
        import tkinter as tk
        from meta_ads_transformer_complete import MetaAdsTransformerComplete
        
        root = tk.Tk()
        root.withdraw()
        app = MetaAdsTransformerComplete(root)
        
        # Test date filtering variables
        filtering_vars = [
            'date_filter_active', 'filter_start_date', 'filter_end_date',
            'selected_month', 'selected_period'
        ]
        
        for var in filtering_vars:
            if hasattr(app, var):
                print(f"✓ {var} variable exists")
            else:
                print(f"✗ {var} variable missing")
                return False
        
        # Test UI components
        ui_components = [
            'filter_status_label', 'month_combo', 'start_date_entry', 'end_date_entry'
        ]
        
        for component in ui_components:
            if hasattr(app, component):
                print(f"✓ {component} UI component exists")
            else:
                print(f"✗ {component} UI component missing")
                return False
        
        # Test filtering methods
        filtering_methods = [
            'populate_month_dropdown', 'on_month_selected', 'apply_period_filter',
            'apply_custom_range', 'apply_date_filter', 'clear_date_filters',
            'get_filtered_data', 'refresh_filtered_displays'
        ]
        
        for method in filtering_methods:
            if hasattr(app, method):
                print(f"✓ {method} method exists")
            else:
                print(f"✗ {method} method missing")
                return False
        
        root.destroy()
        
        print(f"\n🎉 ALL DATE FILTERING COMPONENTS PRESENT!")
        return True
        
    except Exception as e:
        print(f"✗ Error testing date filtering UI: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_date_filtering_functionality():
    """Test date filtering functionality"""
    print("\n" + "=" * 60)
    print("TESTING DATE FILTERING FUNCTIONALITY")
    print("=" * 60)
    
    try:
        import tkinter as tk
        from datetime import datetime, timedelta
        from meta_ads_transformer_complete import MetaAdsTransformerComplete
        
        root = tk.Tk()
        root.withdraw()
        app = MetaAdsTransformerComplete(root)
        
        # Test initial state
        if not app.date_filter_active:
            print("✓ Initial filter state is inactive")
        else:
            print("✗ Initial filter state should be inactive")
            return False
        
        # Test period filter application
        print("\nTesting period filter application...")
        app.apply_period_filter(30)  # Last 30 days
        
        if app.date_filter_active:
            print("✓ Period filter activated successfully")
        else:
            print("✗ Period filter not activated")
            return False
        
        if app.filter_start_date and app.filter_end_date:
            print("✓ Filter dates set correctly")
            print(f"   Start: {app.filter_start_date}")
            print(f"   End: {app.filter_end_date}")
        else:
            print("✗ Filter dates not set")
            return False
        
        # Test custom range application
        print("\nTesting custom range application...")
        app.start_date_entry.insert(0, "2024-01-01")
        app.end_date_entry.insert(0, "2024-01-31")
        app.apply_custom_range()
        
        if app.date_filter_active:
            print("✓ Custom range filter applied successfully")
        else:
            print("✗ Custom range filter not applied")
            return False
        
        # Test filter clearing
        print("\nTesting filter clearing...")
        app.clear_date_filters()
        
        if not app.date_filter_active:
            print("✓ Filters cleared successfully")
        else:
            print("✗ Filters not cleared properly")
            return False
        
        root.destroy()
        
        print(f"\n🎉 DATE FILTERING FUNCTIONALITY WORKING!")
        return True
        
    except Exception as e:
        print(f"✗ Error testing date filtering functionality: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_filtered_data_processing():
    """Test that data processing works with filters"""
    print("\n" + "=" * 60)
    print("TESTING FILTERED DATA PROCESSING")
    print("=" * 60)
    
    try:
        import tkinter as tk
        import pandas as pd
        from datetime import datetime, timedelta
        from meta_ads_transformer_complete import MetaAdsTransformerComplete
        
        root = tk.Tk()
        root.withdraw()
        app = MetaAdsTransformerComplete(root)
        
        # Create test data
        print("Creating test data...")
        test_dates = pd.date_range(start='2024-01-01', end='2024-03-31', freq='D')
        test_data = pd.DataFrame({
            'Day': test_dates,
            'Campaign': ['Test Campaign'] * len(test_dates),
            'Cost': [100.0] * len(test_dates),
            'Clicks': [50] * len(test_dates),
            'Impr.': [1000] * len(test_dates),
            'Conversions': [5] * len(test_dates),
            'CTR': [0.05] * len(test_dates),
            'CPC': [2.0] * len(test_dates)
        })
        
        app.gads_raw_data = test_data
        app.current_data_source = "google"
        
        print(f"✓ Test data created: {len(test_data)} records")
        
        # Test filtering
        print("\nTesting data filtering...")
        
        # Apply 30-day filter
        app.apply_period_filter(30)
        filtered_data = app.get_filtered_data("google")
        
        if filtered_data is not None:
            print(f"✓ Filtered data returned: {len(filtered_data)} records")
            
            # Check that filtered data is actually filtered
            if len(filtered_data) < len(test_data):
                print("✓ Data properly filtered (fewer records)")
            else:
                print("⚠ Filter may not be working (same number of records)")
        else:
            print("✗ No filtered data returned")
            return False
        
        # Test with no data in range
        print("\nTesting filter with no data in range...")
        app.apply_date_filter(
            datetime(2025, 01, 01).date(),
            datetime(2025, 01, 31).date(),
            "Future dates"
        )
        
        future_filtered = app.get_filtered_data("google")
        if future_filtered is not None and len(future_filtered) == 0:
            print("✓ Empty result for out-of-range dates")
        else:
            print("✗ Should return empty data for out-of-range dates")
        
        root.destroy()
        
        print(f"\n🎉 FILTERED DATA PROCESSING WORKING!")
        return True
        
    except Exception as e:
        print(f"✗ Error testing filtered data processing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_with_analytics():
    """Test that filtering integrates with analytical views"""
    print("\n" + "=" * 60)
    print("TESTING INTEGRATION WITH ANALYTICS")
    print("=" * 60)
    
    try:
        import tkinter as tk
        from meta_ads_transformer_complete import MetaAdsTransformerComplete
        
        root = tk.Tk()
        root.withdraw()
        app = MetaAdsTransformerComplete(root)
        
        # Test that analytical methods accept filtered data
        analytical_methods = [
            'process_meta_ads_data', 'process_google_ads_data',
            'generate_meta_quality_report', 'generate_gads_quality_report',
            'update_gads_validation_display', 'update_gads_deep_dive_display',
            'update_gads_temporal_display'
        ]
        
        for method in analytical_methods:
            if hasattr(app, method):
                print(f"✓ {method} available for filtered analysis")
            else:
                print(f"✗ {method} missing")
                return False
        
        # Test refresh method
        if hasattr(app, 'refresh_filtered_displays'):
            print("✓ Refresh method available for real-time updates")
        else:
            print("✗ Refresh method missing")
            return False
        
        root.destroy()
        
        print(f"\n🎉 ANALYTICS INTEGRATION COMPLETE!")
        return True
        
    except Exception as e:
        print(f"✗ Error testing analytics integration: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all date filtering tests"""
    print("📅 ADVANCED DATE FILTERING SYSTEM VALIDATION")
    print("=" * 80)
    print("Testing comprehensive date filtering capabilities:")
    print("• Month selection dropdown")
    print("• Predefined time period buttons (14, 30, 45, 60, 90 days)")
    print("• Custom date range picker")
    print("• Real-time data updates across all views")
    print("• Integration with analytical features")
    print("=" * 80)
    
    # Run all tests
    ui_success = test_date_filtering_ui_components()
    functionality_success = test_date_filtering_functionality()
    processing_success = test_filtered_data_processing()
    integration_success = test_integration_with_analytics()
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 DATE FILTERING SYSTEM SUMMARY")
    print("=" * 80)
    
    print(f"UI Components: {'✅ PASS' if ui_success else '❌ FAIL'}")
    print(f"Filtering Functionality: {'✅ PASS' if functionality_success else '❌ FAIL'}")
    print(f"Data Processing: {'✅ PASS' if processing_success else '❌ FAIL'}")
    print(f"Analytics Integration: {'✅ PASS' if integration_success else '❌ FAIL'}")
    
    all_success = ui_success and functionality_success and processing_success and integration_success
    
    if all_success:
        print(f"\n🎉 ADVANCED DATE FILTERING SYSTEM FULLY IMPLEMENTED!")
        print(f"   ✅ Month selection dropdown with auto-population")
        print(f"   ✅ Quick period buttons (14, 30, 45, 60, 90 days)")
        print(f"   ✅ Custom date range picker with validation")
        print(f"   ✅ Real-time updates across all analytical views")
        print(f"   ✅ Filter state management and clear functionality")
        print(f"   ✅ Integration with Dashboard Validation, Deep Dive, and Temporal Analysis")
        print(f"\n🚀 Ready for advanced time-based campaign analysis!")
    else:
        print(f"\n❌ Some date filtering functionality needs attention.")
        
    return all_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
