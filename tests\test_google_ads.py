#!/usr/bin/env python3
"""
Test script for Google Ads functionality in Enhanced Ads Transformer
"""

import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os

# Add the current directory to the path to import our module
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_google_ads_data_loading():
    """Test loading and validating Google Ads CSV data"""
    print("Testing Google Ads Data Loading...")
    print("=" * 50)
    
    # Load the Google Ads CSV file
    try:
        df = pd.read_csv('gads.csv')
        print(f"✓ Successfully loaded CSV with {len(df)} rows")
        
        # Display basic info
        print(f"✓ Columns: {list(df.columns)}")
        print(f"✓ Date range: {df['Date'].min()} to {df['Date'].max()}")
        print(f"✓ Unique campaigns: {df['Campaign ID'].nunique()}")
        
        # Test data validation
        df['Date'] = pd.to_datetime(df['Date'], errors='coerce')
        numeric_columns = ['Cost', 'Impressions', 'Clicks', 'Conversions']
        
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
                print(f"✓ {col}: {df[col].sum():,.2f} total")
        
        # Calculate key metrics
        total_cost = df['Cost'].sum()
        total_clicks = df['Clicks'].sum()
        total_impressions = df['Impressions'].sum()
        total_conversions = df['Conversions'].sum()
        
        avg_ctr = (total_clicks / total_impressions * 100) if total_impressions > 0 else 0
        avg_cpc = (total_cost / total_clicks) if total_clicks > 0 else 0
        conv_rate = (total_conversions / total_clicks * 100) if total_clicks > 0 else 0
        
        print(f"\nKey Performance Metrics:")
        print(f"• Total Cost: ${total_cost:,.2f}")
        print(f"• Total Clicks: {total_clicks:,.0f}")
        print(f"• Total Impressions: {total_impressions:,.0f}")
        print(f"• Total Conversions: {total_conversions:,.0f}")
        print(f"• Average CTR: {avg_ctr:.2f}%")
        print(f"• Average CPC: ${avg_cpc:.2f}")
        print(f"• Conversion Rate: {conv_rate:.2f}%")
        
        return True
        
    except Exception as e:
        print(f"✗ Error loading Google Ads data: {e}")
        return False

def test_google_ads_aggregation():
    """Test Google Ads data aggregation functionality"""
    print("\n\nTesting Google Ads Aggregation...")
    print("=" * 50)
    
    try:
        # Load data
        df = pd.read_csv('gads.csv')
        df['Date'] = pd.to_datetime(df['Date'], errors='coerce')
        
        # Convert numeric columns
        numeric_columns = ['Cost', 'Impressions', 'Clicks', 'Conversions']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Test daily aggregation
        daily_grouped = df.groupby(df['Date'].dt.to_period('D')).agg({
            'Cost': 'sum',
            'Impressions': 'sum',
            'Clicks': 'sum',
            'Conversions': 'sum',
            'Campaign ID': 'count'
        }).reset_index()
        
        print(f"✓ Daily aggregation: {len(daily_grouped)} days")
        print(f"✓ Sample daily data:")
        print(daily_grouped.head(3).to_string(index=False))
        
        # Test monthly aggregation
        monthly_grouped = df.groupby(df['Date'].dt.to_period('M')).agg({
            'Cost': 'sum',
            'Impressions': 'sum',
            'Clicks': 'sum',
            'Conversions': 'sum',
            'Campaign ID': 'count'
        }).reset_index()
        
        print(f"\n✓ Monthly aggregation: {len(monthly_grouped)} months")
        print(f"✓ Sample monthly data:")
        print(monthly_grouped.head().to_string(index=False))
        
        # Test campaign analysis
        campaign_analysis = df.groupby('Campaign Name').agg({
            'Cost': 'sum',
            'Clicks': 'sum',
            'Conversions': 'sum'
        }).sort_values('Conversions', ascending=False)
        
        print(f"\n✓ Top 5 campaigns by conversions:")
        print(campaign_analysis.head().to_string())
        
        return True
        
    except Exception as e:
        print(f"✗ Error in aggregation testing: {e}")
        return False

def main():
    """Run all tests"""
    print("Enhanced Ads Transformer - Google Ads Testing")
    print("=" * 60)
    
    # Test data loading
    loading_success = test_google_ads_data_loading()
    
    # Test aggregation
    aggregation_success = test_google_ads_aggregation()
    
    # Summary
    print("\n\nTest Summary:")
    print("=" * 30)
    print(f"Data Loading: {'✓ PASS' if loading_success else '✗ FAIL'}")
    print(f"Aggregation: {'✓ PASS' if aggregation_success else '✗ FAIL'}")
    
    if loading_success and aggregation_success:
        print("\n🎉 All tests passed! Google Ads functionality is working correctly.")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()
