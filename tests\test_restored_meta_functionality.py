#!/usr/bin/env python3
"""
Test script to validate the restored comprehensive Meta Ads functionality
"""

import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os

def test_meta_ads_comprehensive_loading():
    """Test comprehensive Meta Ads data loading with all 21 columns"""
    print("Testing Comprehensive Meta Ads Data Loading...")
    print("=" * 60)
    
    try:
        # Load Meta Ads data
        df = pd.read_csv('meta ads full.csv')
        
        # Skip summary row (first row)
        if len(df) > 1:
            df = df.iloc[1:].copy()
        
        # Clean column names
        df.columns = [c.strip() for c in df.columns]
        
        print(f"✓ Loaded {len(df)} Meta Ads records")
        print(f"✓ Available columns: {len(df.columns)}")
        
        # Show all available columns
        print(f"\n📋 All Available Columns:")
        for i, col in enumerate(df.columns, 1):
            print(f"   {i:2d}. {col}")
        
        # Expected comprehensive columns
        expected_columns = [
            'Reporting starts', 'Reporting ends', 'Ad name', 'Ad delivery', 'Ad Set Name',
            'Bid', 'Bid type', 'Ad set budget', 'Ad set budget type', 'Last significant edit',
            'Attribution setting', 'Results', 'Result indicator', 'Reach', 'Impressions',
            'Cost per results', 'Quality ranking', 'Engagement rate ranking', 
            'Conversion rate ranking', 'Amount spent (USD)', 'Ends'
        ]
        
        available_columns = [col for col in expected_columns if col in df.columns]
        missing_columns = [col for col in expected_columns if col not in df.columns]
        
        print(f"\n📊 Column Analysis:")
        print(f"   • Available: {len(available_columns)}/{len(expected_columns)} expected columns")
        print(f"   • Coverage: {(len(available_columns)/len(expected_columns)*100):.1f}%")
        
        if missing_columns:
            print(f"\n❌ Missing Expected Columns:")
            for col in missing_columns:
                print(f"   • {col}")
        
        # Test data richness
        print(f"\n🔍 Data Richness Analysis:")
        
        if 'Ad name' in df.columns:
            unique_ads = df['Ad name'].nunique()
            print(f"   • Unique ads: {unique_ads:,}")
        
        if 'Ad Set Name' in df.columns:
            unique_ad_sets = df['Ad Set Name'].nunique()
            print(f"   • Unique ad sets: {unique_ad_sets:,}")
        
        if 'Ad delivery' in df.columns:
            delivery_statuses = df['Ad delivery'].value_counts()
            print(f"   • Delivery statuses: {len(delivery_statuses)} types")
            for status, count in delivery_statuses.head(3).items():
                print(f"     - {status}: {count:,} ads")
        
        if 'Bid type' in df.columns:
            bid_types = df['Bid type'].value_counts()
            print(f"   • Bid types: {len(bid_types)} types")
            for bid_type, count in bid_types.head(3).items():
                print(f"     - {bid_type}: {count:,} ads")
        
        return True, available_columns, missing_columns
        
    except Exception as e:
        print(f"✗ Meta Ads comprehensive loading failed: {e}")
        import traceback
        traceback.print_exc()
        return False, [], []

def test_meta_ads_enhanced_analytics():
    """Test enhanced Meta Ads analytics capabilities"""
    print("\n\nTesting Enhanced Meta Ads Analytics...")
    print("=" * 60)
    
    try:
        # Load and process data
        df = pd.read_csv('meta ads full.csv')
        if len(df) > 1:
            df = df.iloc[1:].copy()
        df.columns = [c.strip() for c in df.columns]
        
        # Convert data types
        df['Reporting ends'] = pd.to_datetime(df['Reporting ends'], errors='coerce')
        numeric_columns = ['Amount spent (USD)', 'Results', 'Reach', 'Impressions', 'Bid', 'Ad set budget', 'Cost per results']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Remove invalid data
        df = df[df['Reporting ends'].notnull()].copy()
        
        print(f"✓ Processed {len(df)} valid records")
        
        # Test ad-level performance analysis
        if 'Ad name' in df.columns:
            ad_analysis = df.groupby('Ad name').agg({
                'Amount spent (USD)': 'sum',
                'Results': 'sum',
                'Reach': 'sum',
                'Impressions': 'sum'
            }).reset_index()
            
            # Calculate performance metrics
            ad_analysis['Cost_Per_Result'] = np.where(
                ad_analysis['Results'] > 0,
                ad_analysis['Amount spent (USD)'] / ad_analysis['Results'],
                0
            )
            
            ad_analysis['Frequency'] = np.where(
                ad_analysis['Reach'] > 0,
                ad_analysis['Impressions'] / ad_analysis['Reach'],
                0
            )
            
            ad_analysis['CPM'] = np.where(
                ad_analysis['Impressions'] > 0,
                (ad_analysis['Amount spent (USD)'] / ad_analysis['Impressions']) * 1000,
                0
            )
            
            # Sort by performance
            ad_analysis = ad_analysis.sort_values('Cost_Per_Result', ascending=True)
            
            print(f"\n🎯 Ad Performance Analysis:")
            print(f"   • Total ads analyzed: {len(ad_analysis):,}")
            print(f"   • Best performing ad: {ad_analysis.iloc[0]['Ad name'][:40]}")
            print(f"     - Cost per result: ${ad_analysis.iloc[0]['Cost_Per_Result']:.2f}")
            print(f"     - Total spend: ${ad_analysis.iloc[0]['Amount spent (USD)']:,.2f}")
            print(f"     - Total results: {ad_analysis.iloc[0]['Results']:,.0f}")
        
        # Test delivery status analysis
        if 'Ad delivery' in df.columns:
            delivery_analysis = df.groupby('Ad delivery').agg({
                'Amount spent (USD)': 'sum',
                'Results': 'sum',
                'Ad name': 'count'
            }).reset_index()
            
            delivery_analysis['Cost_Per_Result'] = np.where(
                delivery_analysis['Results'] > 0,
                delivery_analysis['Amount spent (USD)'] / delivery_analysis['Results'],
                0
            )
            
            print(f"\n📊 Delivery Status Analysis:")
            for _, row in delivery_analysis.iterrows():
                print(f"   • {row['Ad delivery']}: {row['Ad name']:,} ads, ${row['Amount spent (USD)']:,.2f} spend")
        
        # Test enhanced aggregation
        monthly_agg = df.groupby(df['Reporting ends'].dt.to_period('M')).agg({
            'Amount spent (USD)': 'sum',
            'Results': 'sum',
            'Reach': 'sum',
            'Impressions': 'sum',
            'Ad name': 'count'
        }).reset_index()
        
        # Calculate derived metrics
        monthly_agg['Frequency'] = np.where(
            monthly_agg['Reach'] > 0,
            monthly_agg['Impressions'] / monthly_agg['Reach'],
            0
        )
        
        monthly_agg['Cost_Per_Result'] = np.where(
            monthly_agg['Results'] > 0,
            monthly_agg['Amount spent (USD)'] / monthly_agg['Results'],
            0
        )
        
        monthly_agg['CPM'] = np.where(
            monthly_agg['Impressions'] > 0,
            (monthly_agg['Amount spent (USD)'] / monthly_agg['Impressions']) * 1000,
            0
        )
        
        print(f"\n📅 Enhanced Monthly Analysis:")
        for _, row in monthly_agg.iterrows():
            print(f"   • {row['Reporting ends']}: ${row['Amount spent (USD)']:,.2f} spend, {row['Results']:,.0f} results")
            print(f"     - CPM: ${row['CPM']:.2f}, Frequency: {row['Frequency']:.2f}, Cost/Result: ${row['Cost_Per_Result']:.2f}")
        
        return True
        
    except Exception as e:
        print(f"✗ Enhanced analytics test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run comprehensive Meta Ads functionality tests"""
    print("🚀 RESTORED META ADS FUNCTIONALITY VALIDATION")
    print("=" * 80)
    print("Testing the restored comprehensive Meta Ads functionality")
    print("with all 21 columns and advanced analytics capabilities.")
    print("=" * 80)
    
    # Test comprehensive loading
    loading_success, available_cols, missing_cols = test_meta_ads_comprehensive_loading()
    
    # Test enhanced analytics
    analytics_success = test_meta_ads_enhanced_analytics()
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 VALIDATION SUMMARY")
    print("=" * 80)
    
    print(f"Data Loading: {'✅ PASS' if loading_success else '❌ FAIL'}")
    print(f"Enhanced Analytics: {'✅ PASS' if analytics_success else '❌ FAIL'}")
    
    if loading_success:
        print(f"\n📊 Data Richness Restored:")
        print(f"   • Available columns: {len(available_cols)}/21 expected")
        print(f"   • Column coverage: {(len(available_cols)/21*100):.1f}%")
        
        if missing_cols:
            print(f"   • Missing columns: {len(missing_cols)}")
        else:
            print(f"   • ✅ ALL EXPECTED COLUMNS AVAILABLE!")
    
    if loading_success and analytics_success:
        print(f"\n🎉 SUCCESS: Meta Ads functionality has been fully restored!")
        print(f"   • Comprehensive data loading with all available columns")
        print(f"   • Enhanced analytics and performance metrics")
        print(f"   • Delivery status and bid strategy analysis")
        print(f"   • Advanced aggregation with derived metrics")
        print(f"   • Campaign structure and hierarchy analysis")
    else:
        print(f"\n❌ Some functionality still needs attention.")

if __name__ == "__main__":
    main()
