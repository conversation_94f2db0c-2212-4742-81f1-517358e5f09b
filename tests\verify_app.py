#!/usr/bin/env python3
"""
Quick verification script to test the Enhanced Ads Transformer application
"""

import sys
import traceback

def test_imports():
    """Test that all required imports work"""
    print("Testing imports...")
    try:
        import tkinter as tk
        from tkinter import ttk, filedialog, messagebox
        import pandas as pd
        import numpy as np
        from datetime import datetime
        import os
        print("✓ All imports successful")
        return True
    except Exception as e:
        print(f"✗ Import error: {e}")
        return False

def test_class_instantiation():
    """Test that the main class can be instantiated"""
    print("Testing class instantiation...")
    try:
        # Import the main class
        from meta_ads_transformer_fixed import EnhancedAdsTransformer
        
        # Create a root window (but don't show it)
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        # Try to instantiate the class
        app = EnhancedAdsTransformer(root)
        print("✓ EnhancedAdsTransformer class instantiated successfully")
        
        # Clean up
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ Class instantiation error: {e}")
        traceback.print_exc()
        return False

def test_data_loading_methods():
    """Test that data loading methods exist and are callable"""
    print("Testing data loading methods...")
    try:
        from meta_ads_transformer_fixed import EnhancedAdsTransformer
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        app = EnhancedAdsTransformer(root)
        
        # Check that key methods exist
        methods_to_check = [
            'load_and_validate_meta_csv',
            'load_and_validate_gads_csv',
            'aggregate_meta_by_period',
            'aggregate_gads_by_period',
            'generate_quality_report',
            'generate_gads_quality_report'
        ]
        
        for method_name in methods_to_check:
            if hasattr(app, method_name):
                print(f"✓ Method {method_name} exists")
            else:
                print(f"✗ Method {method_name} missing")
                return False
        
        root.destroy()
        print("✓ All required methods exist")
        return True
        
    except Exception as e:
        print(f"✗ Method checking error: {e}")
        traceback.print_exc()
        return False

def test_csv_files_exist():
    """Test that required CSV files exist"""
    print("Testing CSV file availability...")
    try:
        import os
        
        required_files = ['meta ads full.csv', 'gads.csv']
        
        for file_name in required_files:
            if os.path.exists(file_name):
                print(f"✓ {file_name} exists")
            else:
                print(f"✗ {file_name} missing")
                return False
        
        print("✓ All required CSV files exist")
        return True
        
    except Exception as e:
        print(f"✗ File checking error: {e}")
        return False

def main():
    """Run all verification tests"""
    print("🔍 ENHANCED ADS TRANSFORMER - APPLICATION VERIFICATION")
    print("=" * 60)
    
    tests = [
        ("Import Dependencies", test_imports),
        ("Class Instantiation", test_class_instantiation),
        ("Method Availability", test_data_loading_methods),
        ("CSV File Availability", test_csv_files_exist)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        result = test_func()
        results.append((test_name, result))
    
    # Summary
    print("\n" + "=" * 60)
    print("VERIFICATION SUMMARY")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print(f"\n🎉 ALL TESTS PASSED!")
        print("The Enhanced Ads Transformer application is ready to run.")
        print("\nTo launch the application:")
        print("python meta_ads_transformer_fixed.py")
    else:
        print(f"\n❌ Some tests failed. Please check the errors above.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
