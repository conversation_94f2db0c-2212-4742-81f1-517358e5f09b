#!/usr/bin/env python3
"""
Theme Preview Tool for Meta Ads Transformer
Test different themes before applying them to the main application
"""

import tkinter as tk
from tkinter import ttk
import sys

def create_preview_window(theme_name):
    """Create a preview window with the specified theme"""
    try:
        from ttkthemes import ThemedTk, ThemedStyle
        
        # Create themed window
        root = ThemedTk(theme=theme_name)
        root.title(f"Theme Preview: {theme_name.title()}")
        root.geometry("600x500")
        
        # Main container
        main_frame = ttk.Frame(root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text=f"{theme_name.title()} Theme Preview", 
                               font=('Segoe UI', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # File Operations Section (similar to your app)
        file_ops_frame = ttk.LabelFrame(main_frame, text="📁 File Operations", padding="10")
        file_ops_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(file_ops_frame, text="Load Meta Ads CSV", width=28).pack(pady=5)
        ttk.Label(file_ops_frame, text="No file loaded", foreground='gray').pack(pady=2)
        
        # Process button (like your accent button)
        process_btn = ttk.Button(file_ops_frame, text="🔍 Process & Validate Data", 
                                width=28, style='Accent.TButton')
        process_btn.pack(pady=(5, 2))
        
        ttk.Label(file_ops_frame, text="Click to process loaded data", 
                 font=('Arial', 8), foreground='gray').pack()
        
        # Data display section
        data_frame = ttk.LabelFrame(main_frame, text="📊 Data Display", padding="10")
        data_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Sample treeview
        columns = ('Campaign', 'Cost', 'Clicks', 'Impressions')
        tree = ttk.Treeview(data_frame, columns=columns, show='headings', height=8)
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120, anchor=tk.CENTER)
        
        # Sample data
        sample_data = [
            ("Campaign A", "$123.45", "45", "1,234"),
            ("Campaign B", "$67.89", "23", "890"),
            ("Campaign C", "$234.56", "78", "2,345"),
        ]
        
        for item in sample_data:
            tree.insert('', tk.END, values=item)
        
        tree.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Control buttons
        button_frame = ttk.Frame(data_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="Export Data").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Clear Filters").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Refresh").pack(side=tk.LEFT)
        
        # Status bar
        status_frame = ttk.Frame(root)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        status_label = ttk.Label(status_frame, text=f"Preview of {theme_name} theme - Close window to try another theme",
                                relief=tk.SUNKEN, anchor=tk.W, padding="5")
        status_label.pack(fill=tk.X)
        
        # Configure accent button style for this theme
        style = ThemedStyle(root)
        
        # Get theme colors (these are approximations)
        theme_colors = {
            'arc': {'accent': '#5294e2', 'hover': '#4189df'},
            'adapta': {'accent': '#00bcd4', 'hover': '#00acc1'},
            'equilux': {'accent': '#64b5f6', 'hover': '#42a5f5'},
            'yaru': {'accent': '#e95420', 'hover': '#d73502'},
            'breeze': {'accent': '#3daee9', 'hover': '#2980b9'},
        }
        
        colors = theme_colors.get(theme_name, {'accent': '#0078d4', 'hover': '#106ebe'})
        
        style.configure('Accent.TButton',
                       font=('Segoe UI', 10, 'bold'),
                       foreground='white')
        
        style.map('Accent.TButton',
                 background=[('active', colors['hover']),
                            ('pressed', colors['accent'])])
        
        root.mainloop()
        return True
        
    except ImportError:
        print("❌ ttkthemes not installed. Run: pip install ttkthemes")
        return False
    except Exception as e:
        print(f"❌ Error creating preview for {theme_name}: {e}")
        return False

def main():
    """Main theme preview function"""
    print("🎨 Meta Ads Transformer - Theme Preview Tool")
    print("=" * 45)
    
    # Check if ttkthemes is available
    try:
        from ttkthemes import ThemedStyle
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        style = ThemedStyle(root)
        available_themes = sorted(style.theme_names())
        root.destroy()
        
    except ImportError:
        print("❌ ttkthemes not installed.")
        print("Install with: pip install ttkthemes")
        print("Or run: python install_themes.py")
        return
    
    # Recommended themes for your app
    recommended_themes = ['arc', 'adapta', 'equilux', 'yaru', 'breeze']
    available_recommended = [t for t in recommended_themes if t in available_themes]
    
    print(f"📋 Found {len(available_themes)} available themes")
    print(f"🌟 Recommended themes: {', '.join(available_recommended)}")
    
    while True:
        print(f"\n🎨 Choose a theme to preview:")
        print("   Recommended themes:")
        for i, theme in enumerate(available_recommended, 1):
            print(f"     {i}. {theme}")
        
        print(f"\n   Other options:")
        print(f"     A. Show all {len(available_themes)} themes")
        print(f"     Q. Quit")
        
        choice = input(f"\nEnter your choice: ").strip().lower()
        
        if choice == 'q':
            break
        elif choice == 'a':
            print(f"\n📋 All available themes:")
            for i, theme in enumerate(available_themes, 1):
                print(f"  {i:2d}. {theme}")
            
            try:
                theme_num = int(input(f"\nEnter theme number (1-{len(available_themes)}): "))
                if 1 <= theme_num <= len(available_themes):
                    selected_theme = available_themes[theme_num - 1]
                    print(f"\n🎨 Opening preview for '{selected_theme}' theme...")
                    create_preview_window(selected_theme)
                else:
                    print("❌ Invalid theme number")
            except ValueError:
                print("❌ Please enter a valid number")
        else:
            try:
                theme_num = int(choice)
                if 1 <= theme_num <= len(available_recommended):
                    selected_theme = available_recommended[theme_num - 1]
                    print(f"\n🎨 Opening preview for '{selected_theme}' theme...")
                    create_preview_window(selected_theme)
                else:
                    print("❌ Invalid choice")
            except ValueError:
                print("❌ Please enter a valid number")
    
    print("\n👋 Thanks for using the theme preview tool!")

if __name__ == "__main__":
    main()
